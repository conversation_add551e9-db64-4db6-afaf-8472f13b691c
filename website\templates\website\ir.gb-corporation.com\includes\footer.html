{% load static %}
<!-- Main container with white background -->
<div style="background: #ffffff !important;">
    <!-- Footer Section -->
    <footer style="background: #ffffff !important; width: 100%;">
        <div style="border-top: 1px solid #e0e0e0; background: #ffffff !important;">
            <div style="max-width: 1000px; margin: 0 auto; padding: 30px 20px; background: #ffffff !important;">
                <div style="display: flex; flex-direction: column; gap: 20px;">
                    <!-- Top Row -->
                    <div class="newsletter-section" style="display: flex; align-items: center; flex-wrap: wrap; gap: 10px;">
                        <span style="font-size: 14px; color: #333; margin-right: 15px; margin-bottom: 10px;">
                            {% if ui_text.subscribe_newsletter %}{{ ui_text.subscribe_newsletter.text_en }}{% else %}Subscribe to newsletter{% endif %}
                        </span>
                        <form id="newsletter-form" class="input-group" style="display: flex; flex-wrap: wrap; gap: 10px; width: 100%; max-width: 400px;">
                            {% csrf_token %}
                            <!-- Honeypot fields for bot detection (hidden from users) -->
                            <input type="text" name="website_url" value="" style="position: absolute; left: -9999px; top: -9999px; width: 1px; height: 1px; opacity: 0;" tabindex="-1" autocomplete="off" />
                            <input type="text" name="company_name" value="" style="position: absolute; left: -9999px; top: -9999px; width: 1px; height: 1px; opacity: 0;" tabindex="-1" autocomplete="off" />
                            <input type="text" name="phone_number" value="" style="position: absolute; left: -9999px; top: -9999px; width: 1px; height: 1px; opacity: 0;" tabindex="-1" autocomplete="off" />
                            <input type="text" name="full_address" value="" style="position: absolute; left: -9999px; top: -9999px; width: 1px; height: 1px; opacity: 0;" tabindex="-1" autocomplete="off" />

                            <input type="email" id="newsletter-email" placeholder="{% if ui_text.email_placeholder %}{{ ui_text.email_placeholder.text_en }}{% else %}E-mail{% endif %}" style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; flex-grow: 1; font-size: 14px; margin-bottom: 10px; min-width: 200px;" autocomplete="email">
                            <button type="submit" id="newsletter-submit-btn" style="background-color: #0066cc; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px; margin-bottom: 10px; min-width: 100px;">
                                <span class="btn-text">{% if ui_text.subscribe %}{{ ui_text.subscribe.text_en }}{% else %}Subscribe{% endif %}</span>
                                <span class="btn-loading" style="display: none;">...</span>
                            </button>
                            <div id="newsletter-message" style="display: none; margin-top: 5px; font-size: 12px; width: 100%;"></div>
                            <div id="newsletter-status" style="display: none; margin-top: 5px; font-size: 12px; width: 100%; color: #666;"></div>
                        </form>
                    </div>

                    <!-- Bottom Row -->
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <span style="font-size: 14px; color: #333;">
                                {% if ui_text.investor_relations %}
                                    {{ ui_text.investor_relations.text_en }}
                                {% else %}
                                    Investor Relations
                                {% endif %}
                            </span>
                            <span style="font-size: 14px; color: #666; margin-left: 8px;">
                                {% if primary_contact %}
                                    {{ primary_contact.phone }}
                                {% else %}
                                    +20 (0) 2 3910 0485
                                {% endif %}
                            </span>
                        </div>
                        <span style="font-size: 14px; color: #666;">
                            © {% if company_settings %}{{ company_settings.founding_year }}{% else %}2018{% endif %} 
                            {% if company_settings %}{{ company_settings.name }}{% else %}GB Corp{% endif %}. 
                            {% if ui_text.all_rights_reserved %}{{ ui_text.all_rights_reserved.text_en }}{% else %}All rights reserved{% endif %}.
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>

<style>
@media (min-width: 768px) {
    /* Desktop styles */
    .newsletter-section {
        flex-wrap: nowrap !important;
    }
    
    .input-group {
        flex-wrap: nowrap !important;
    }
    
    /* Bottom row for desktop */
    footer .bottom-row {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
    }
}

@media (max-width: 767px) {
    /* Mobile styles */
    .newsletter-section {
        flex-direction: column !important;
        justify-content: center !important;
        text-align: center !important;
        width: 100% !important;
        padding: 0 10px !important;
    }
    
    .newsletter-section span {
        width: 100% !important;
        text-align: center !important;
        margin-right: 0 !important;
        font-size: 16px !important;
        margin-bottom: 15px !important;
    }
    
    .input-group {
        width: 100% !important;
        max-width: 100% !important;
        justify-content: center !important;
        flex-direction: column !important;
    }
    
    .input-group input {
        width: 100% !important;
        margin-right: 0 !important;
        margin-bottom: 15px !important;
        text-align: center !important;
        padding: 12px !important;
    }
    
    .input-group button {
        width: 100% !important;
        padding: 12px !important;
    }
    
    /* Keep copyright on the right side even on mobile */
    footer div[style*="justify-content: space-between"] {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        flex-wrap: wrap !important;
        gap: 15px !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('newsletter-form');
    const emailInput = document.getElementById('newsletter-email');
    const submitBtn = document.getElementById('newsletter-submit-btn');
    const messageDiv = document.getElementById('newsletter-message');
    const statusDiv = document.getElementById('newsletter-status');

    let emailCheckTimeout;

    // Bot detection tracking variables
    var formLoadTime = Date.now();
    var mouseMovements = [];
    var clicks = [];
    var focusEvents = 0;
    var keyEvents = 0;
    var typingStart = null;
    
    // Email validation function
    function isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    // Bot detection event listeners
    document.addEventListener('mousemove', function(e) {
        mouseMovements.push([e.clientX, e.clientY, Date.now()]);
        if (mouseMovements.length > 50) mouseMovements.shift(); // Keep last 50
    });

    document.addEventListener('click', function(e) {
        clicks.push([e.clientX, e.clientY, Date.now()]);
    });

    // Track form interactions
    if (emailInput) {
        emailInput.addEventListener('focus', function() {
            focusEvents++;
        });

        emailInput.addEventListener('keydown', function() {
            if (typingStart === null) typingStart = Date.now();
            keyEvents++;
        });
    }
    
    // Get CSRF token
    function getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    // Check subscription status
    async function checkSubscriptionStatus(email) {
        try {
            const response = await fetch('/api/newsletter-check/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken(),
                },
                body: JSON.stringify({ email: email })
            });
            
            const data = await response.json();
            
            if (data.success) {
                if (data.subscribed) {
                    statusDiv.style.display = 'block';
                    statusDiv.style.color = '#f0ad4e';
                    statusDiv.textContent = 'This email is already subscribed.';
                    submitBtn.disabled = true;
                    submitBtn.style.opacity = '0.6';
                } else {
                    statusDiv.style.display = 'none';
                    submitBtn.disabled = false;
                    submitBtn.style.opacity = '1';
                }
            }
        } catch (error) {
            console.log('Email check failed:', error);
            statusDiv.style.display = 'none';
            submitBtn.disabled = false;
            submitBtn.style.opacity = '1';
        }
    }
    
    // Real-time email checking
    if (emailInput) {
        emailInput.addEventListener('input', function() {
            const email = this.value.trim();
            
            clearTimeout(emailCheckTimeout);
            statusDiv.style.display = 'none';
            submitBtn.disabled = false;
            submitBtn.style.opacity = '1';
            
            if (email && isValidEmail(email)) {
                emailCheckTimeout = setTimeout(() => {
                    checkSubscriptionStatus(email);
                }, 1000);
            }
        });
    }
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = emailInput.value.trim();
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            
            if (!email) {
                messageDiv.style.display = 'block';
                messageDiv.style.color = '#d9534f';
                messageDiv.textContent = 'Please enter your email address.';
                return;
            }
            
            if (!isValidEmail(email)) {
                messageDiv.style.display = 'block';
                messageDiv.style.color = '#d9534f';
                messageDiv.textContent = 'Please enter a valid email address.';
                return;
            }
            
            // Show loading state
            if (btnText && btnLoading) {
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline';
            } else {
                submitBtn.textContent = 'Subscribing...';
            }
            submitBtn.disabled = true;
            
            messageDiv.style.display = 'block';
            messageDiv.style.color = '#666';
            messageDiv.textContent = 'Subscribing...';

            // Prepare bot detection data
            var now = Date.now();
            var typingDuration = typingStart ? (now - typingStart) / 1000 : 0;

            // Generate validation token
            var token = btoa(String.fromCharCode.apply(null,
                new Uint8Array(crypto.getRandomValues(new Uint8Array(16)))));

            fetch('/api/newsletter-subscribe/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken(),
                },
                body: JSON.stringify({
                    email: email,
                    language: 'en',
                    // Bot detection data
                    form_load_timestamp: formLoadTime / 1000,
                    submission_timestamp: now / 1000,
                    form_interactions: {
                        focus_events: focusEvents,
                        key_events: keyEvents,
                        typing_duration: typingDuration
                    },
                    mouse_movements: {
                        movements: mouseMovements.slice(-10), // Last 10 movements
                        clicks: clicks
                    },
                    js_validation_token: token
                })
            })
            .then(response => response.json())
            .then(data => {
                messageDiv.style.display = 'block';
                if (data.success) {
                    messageDiv.style.color = '#5cb85c';
                    messageDiv.textContent = data.message;
                    emailInput.value = '';
                    statusDiv.style.display = 'none';
                } else {
                    messageDiv.style.color = '#d9534f';
                    messageDiv.textContent = data.message;
                }
            })
            .catch(error => {
                messageDiv.style.display = 'block';
                messageDiv.style.color = '#d9534f';
                messageDiv.textContent = 'An error occurred. Please try again later.';
                console.error('Newsletter subscription error:', error);
            })
            .finally(() => {
                // Reset button state
                if (btnText && btnLoading) {
                    btnText.style.display = 'inline';
                    btnLoading.style.display = 'none';
                } else {
                    submitBtn.textContent = 'Subscribe';
                }
                submitBtn.disabled = false;
            });
        });
    }
});
</script> 