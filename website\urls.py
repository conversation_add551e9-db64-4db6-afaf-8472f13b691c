from django.urls import path, re_path
from django.shortcuts import redirect
from . import views
from . import filings_views
from . import leadership_governance_views

app_name = 'website'

urlpatterns = [
    # Default redirect to English version
    path('', lambda request: redirect('website:en_index'), name='index'),
    
    # Language root pages
    path('en/', views.en_page, kwargs={'page_name': 'index'}, name='en_index'),
    path('ar/', views.ar_page, kwargs={'page_name': 'index'}, name='ar_index'),
    
    # Share Information pages
    path('en/share-information', views.share_information, kwargs={'lang': 'en'}, name='share_information_en'),
    path('ar/share-information', views.share_information, kwargs={'lang': 'ar'}, name='share_information_ar'),
    
    # ESG Policy pages
    path('en/esg-policy', views.esg_policy, kwargs={'language': 'en'}, name='esg_policy_en'),
    path('ar/esg-policy', views.esg_policy, kwargs={'language': 'ar'}, name='esg_policy_ar'),
    
    # Leadership and Governance pages
    path('en/leadership-and-governance', leadership_governance_views.leadership_governance_en, name='leadership_governance_en'),
    path('ar/leadership-and-governance', leadership_governance_views.leadership_governance_ar, name='leadership_governance_ar'),
    
    # Filings pages
    path('en/filings', filings_views.filings_en, name='filings_en'),
    path('ar/filings', filings_views.filings_ar, name='filings_ar'),
    

    
    # Dynamic pages - USED: These are actively used for dynamic page routing
    path('en/<str:page_name>', views.en_page, name='en_page'),
    path('ar/<str:page_name>', views.ar_page, name='ar_page'),
    # POTENTIALLY UNUSED: ar_page_html pattern - ar_page view already handles .html extensions internally
    # path('ar/<str:page_name>.html', views.ar_page, name='ar_page_html'),
    
    # API endpoints (frontend-used only)
    path('api/newsletter-subscribe/', views.newsletter_subscribe, name='newsletter_subscribe'),
    path('api/newsletter-check/', views.newsletter_check_subscription, name='newsletter_check_subscription'),
    path('api/stock-history/', views.stock_history_api, name='stock_history_api'),
    path('api/investmentcalculator', views.investment_calculator, name='investment_calculator'),
    path('api/timeline/', views.timeline_api, name='timeline_api'),
    path('api/timeline/<int:year>/navigate/<str:direction>', views.timeline_api, name='timeline_api'),

    # Legacy API endpoints (now secured) - UNUSED: These are legacy endpoints that are not referenced anywhere in the codebase
    # path('subscribe', views.subscribe, name='legacy_subscribe'),
    # path('create_stock_alert', views.create_stock_alert, name='legacy_stock_alert'),

    # Debug endpoints - UNUSED: Only for debugging, not referenced in production code
    # path('check-context/', views.check_context, name='check_context'),

    # Newsletter management - USED: Referenced in newsletter unsubscribe functionality
    path('newsletter/unsubscribe/<str:token>/', views.newsletter_unsubscribe, name='newsletter_unsubscribe'),

    # Earnings newsletter redirect - UNUSED: No references found in templates or JavaScript
    # path('earnings-newsletter/', views.earnings_newsletter_redirect, name='earnings_newsletter_redirect'),
    # path('earnings-newsletter/<str:period>/<int:year>/', views.earnings_newsletter_redirect, name='earnings_newsletter_redirect_with_params'),

    # Direct database lookup for earnings newsletter - UNUSED: No references found in templates or JavaScript
    # path('newsletter/', views.get_earnings_newsletter, name='get_earnings_newsletter'),
    # path('newsletter/<str:display_name>/', views.get_earnings_newsletter, name='get_earnings_newsletter_with_name'),

    # Earnings results with database details - UNUSED: No references found in templates or JavaScript
    # path('earnings-results/', views.earnings_result_view, name='earnings_result_view'),

    # Direct URL for earnings newsletter (avoids formatting issues) - UNUSED: No references found
    # path('newsletter-direct/<int:result_id>/', views.earnings_newsletter_direct, name='earnings_newsletter_direct'),

    # Super simple redirect to latest newsletter URL - UNUSED: No references found
    # path('latest-newsletter/', views.latest_newsletter_redirect, name='latest_newsletter_redirect'),
    
    # Simple redirects to newsletter and release URLs
    path('newsletter-redirect/', views.newsletter_redirect, name='newsletter_redirect'),
    path('release-redirect/', views.release_redirect, name='release_redirect'),
    

]