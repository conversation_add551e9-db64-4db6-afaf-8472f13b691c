"""
Honeypot Protection System for Newsletter Subscriptions
Provides bot detection and spam prevention mechanisms
"""

import time
import logging
import hashlib
from typing import Dict, List, Optional
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)


class HoneypotProtection:
    """
    Honeypot-based bot detection and spam prevention
    """
    
    def __init__(self):
        self.cache_timeout = getattr(settings, 'HONEYPOT_CACHE_TIMEOUT', 3600)
        self.honeypot_fields = [
            'website_url',  # Hidden field that should remain empty
            'company_name',  # Another hidden field
            'phone_number',  # Yet another hidden field
            'full_address'   # Additional hidden field
        ]
        
    def validate_honeypot_submission(self, request_data: Dict, ip_address: str) -> Dict[str, any]:
        """
        Validate submission against honeypot traps
        
        Returns:
            Dict with validation results and bot detection flags
        """
        checks = {
            'honeypot_triggered': self._check_honeypot_fields(request_data),
            'submission_timing': self._check_submission_timing(request_data, ip_address),
            'form_interaction': self._check_form_interaction_patterns(request_data),
            'javascript_validation': self._check_javascript_validation(request_data),
            'mouse_movement': self._check_mouse_movement_patterns(request_data)
        }
        
        # Calculate bot probability score
        bot_score = self._calculate_bot_score(checks)
        
        # Determine if submission should be blocked
        # Temporarily reduce threshold for testing - was 70, now 90
        is_bot = bot_score >= 90
        
        # Log bot detection
        if is_bot:
            self._log_bot_detection(ip_address, request_data, checks, bot_score)
        
        return {
            'is_bot': is_bot,
            'bot_score': bot_score,
            'checks': checks,
            'block_submission': is_bot,
            'reason': self._get_block_reason(checks) if is_bot else None
        }
    
    def _check_honeypot_fields(self, request_data: Dict) -> Dict[str, any]:
        """
        Check if any honeypot fields were filled (indicates bot)
        """
        triggered_fields = []
        
        for field in self.honeypot_fields:
            if field in request_data and request_data[field]:
                triggered_fields.append(field)
        
        return {
            'triggered': len(triggered_fields) > 0,
            'triggered_fields': triggered_fields,
            'confidence': 'high' if triggered_fields else 'low'
        }
    
    def _check_submission_timing(self, request_data: Dict, ip_address: str) -> Dict[str, any]:
        """
        Check submission timing patterns (too fast = bot)
        """
        # Get form load timestamp if provided
        form_load_time = request_data.get('form_load_timestamp')
        submission_time = request_data.get('submission_timestamp', time.time())
        
        if form_load_time:
            try:
                load_time = float(form_load_time)
                submit_time = float(submission_time)
                fill_duration = submit_time - load_time
                
                # Human users typically take at least 3 seconds to fill a form (reduced from 5)
                too_fast = fill_duration < 3
                # But also suspicious if too slow (over 30 minutes)
                too_slow = fill_duration > 1800
                
                return {
                    'too_fast': too_fast,
                    'too_slow': too_slow,
                    'fill_duration': fill_duration,
                    'confidence': 'high' if too_fast else 'medium' if too_slow else 'low'
                }
            except (ValueError, TypeError):
                pass
        
        # Check rapid successive submissions from same IP
        rapid_key = f"honeypot_rapid_{ip_address}"
        recent_submissions = cache.get(rapid_key, [])
        
        now = time.time()
        # Filter submissions from last 5 minutes
        recent_submissions = [t for t in recent_submissions if now - t < 300]
        recent_submissions.append(now)
        
        cache.set(rapid_key, recent_submissions, 300)
        
        rapid_succession = len(recent_submissions) >= 3
        
        return {
            'too_fast': False,
            'too_slow': False,
            'rapid_succession': rapid_succession,
            'recent_count': len(recent_submissions),
            'confidence': 'high' if rapid_succession else 'low'
        }
    
    def _check_form_interaction_patterns(self, request_data: Dict) -> Dict[str, any]:
        """
        Check for human-like form interaction patterns
        """
        # Check for form interaction data
        interaction_data = request_data.get('form_interactions', {})
        
        suspicious_patterns = []
        
        # Check for missing interaction data (bots often don't simulate this)
        if not interaction_data:
            suspicious_patterns.append('no_interaction_data')
        else:
            # Check for unrealistic interaction patterns
            focus_events = interaction_data.get('focus_events', 0)
            key_events = interaction_data.get('key_events', 0)
            
            # Too few interactions for a real user (made more lenient for newsletter forms)
            if focus_events < 1:  # Reduced from 2 to 1
                suspicious_patterns.append('insufficient_focus_events')

            if key_events < 3:  # Reduced from 5 to 3 (just email typing)
                suspicious_patterns.append('insufficient_key_events')
            
            # Check for impossible typing speed
            typing_duration = interaction_data.get('typing_duration', 0)
            if typing_duration > 0 and key_events > 0:
                typing_speed = key_events / typing_duration  # keys per second
                if typing_speed > 10:  # Unrealistically fast typing
                    suspicious_patterns.append('unrealistic_typing_speed')
        
        return {
            'suspicious_patterns': suspicious_patterns,
            'count': len(suspicious_patterns),
            'confidence': 'high' if len(suspicious_patterns) >= 2 else 'medium' if suspicious_patterns else 'low'
        }
    
    def _check_javascript_validation(self, request_data: Dict) -> Dict[str, any]:
        """
        Check for JavaScript-based validation tokens
        """
        # Check for JavaScript validation token
        js_token = request_data.get('js_validation_token')
        expected_token = request_data.get('expected_js_token')
        
        if not js_token:
            return {
                'missing_token': True,
                'invalid_token': False,
                'confidence': 'medium'
            }
        
        if expected_token and js_token != expected_token:
            return {
                'missing_token': False,
                'invalid_token': True,
                'confidence': 'high'
            }
        
        return {
            'missing_token': False,
            'invalid_token': False,
            'confidence': 'low'
        }
    
    def _check_mouse_movement_patterns(self, request_data: Dict) -> Dict[str, any]:
        """
        Check for mouse movement patterns (bots often don't simulate mouse movement)
        """
        mouse_data = request_data.get('mouse_movements', {})
        
        if not mouse_data:
            return {
                'no_mouse_data': True,
                'unnatural_patterns': False,
                'confidence': 'medium'
            }
        
        # Check for unnatural mouse movement patterns
        movements = mouse_data.get('movements', [])
        clicks = mouse_data.get('clicks', [])
        
        suspicious_patterns = []
        
        # Too few mouse movements for a real user
        if len(movements) < 5:
            suspicious_patterns.append('insufficient_movements')
        
        # No clicks (users typically click on form fields)
        if len(clicks) == 0:
            suspicious_patterns.append('no_clicks')
        
        # Check for perfectly straight line movements (unnatural)
        if len(movements) >= 3:
            straight_lines = 0
            for i in range(2, len(movements)):
                x1, y1 = movements[i-2][:2]
                x2, y2 = movements[i-1][:2]
                x3, y3 = movements[i][:2]
                
                # Check if three consecutive points are in a straight line
                if abs((y3-y1)*(x2-x1) - (y2-y1)*(x3-x1)) < 1:
                    straight_lines += 1
            
            if straight_lines > len(movements) * 0.8:  # More than 80% straight lines
                suspicious_patterns.append('too_many_straight_lines')
        
        return {
            'no_mouse_data': False,
            'unnatural_patterns': len(suspicious_patterns) > 0,
            'suspicious_patterns': suspicious_patterns,
            'confidence': 'high' if len(suspicious_patterns) >= 2 else 'medium' if suspicious_patterns else 'low'
        }
    
    def _calculate_bot_score(self, checks: Dict) -> int:
        """
        Calculate overall bot probability score (0-100)
        """
        score = 0
        
        # Honeypot fields (highest weight)
        if checks['honeypot_triggered']['triggered']:
            score += 90
        
        # Submission timing
        timing = checks['submission_timing']
        if timing.get('too_fast'):
            score += 80
        if timing.get('rapid_succession'):
            score += 60
        if timing.get('too_slow'):
            score += 30
        
        # Form interaction patterns (reduced penalty for testing)
        interaction = checks['form_interaction']
        score += interaction['count'] * 10  # Reduced from 20 to 10
        
        # JavaScript validation (reduced penalties for compatibility)
        js_check = checks['javascript_validation']
        if js_check['missing_token']:
            score += 20  # Reduced from 40 to 20
        if js_check['invalid_token']:
            score += 35  # Reduced from 70 to 35
        
        # Mouse movement patterns (reduced penalties for mobile compatibility)
        mouse = checks['mouse_movement']
        if mouse['no_mouse_data']:
            score += 15  # Reduced from 30 to 15 (mobile users often have no mouse)
        if mouse['unnatural_patterns']:
            score += 25  # Reduced from 50 to 25
        
        return min(score, 100)
    
    def _get_block_reason(self, checks: Dict) -> str:
        """
        Get human-readable reason for blocking
        """
        if checks['honeypot_triggered']['triggered']:
            return 'Bot detection: Hidden fields were filled'
        
        if checks['submission_timing'].get('too_fast'):
            return 'Bot detection: Form submitted too quickly'
        
        if checks['javascript_validation']['invalid_token']:
            return 'Bot detection: Invalid validation token'
        
        if checks['form_interaction']['count'] >= 2:
            return 'Bot detection: Suspicious interaction patterns'
        
        return 'Bot detection: Multiple suspicious indicators'
    
    def _log_bot_detection(self, ip_address: str, request_data: Dict, 
                          checks: Dict, bot_score: int):
        """
        Log bot detection for analysis
        """
        logger.warning(
            f"Bot detected in newsletter subscription: "
            f"IP: {ip_address}, Score: {bot_score}, "
            f"Checks: {checks}, Data: {request_data}"
        )
        
        # Store in cache for admin review
        incident_key = f"bot_incident_{int(time.time())}"
        incident_data = {
            'timestamp': timezone.now().isoformat(),
            'ip_address': ip_address,
            'bot_score': bot_score,
            'checks': checks,
            'request_data': request_data
        }
        cache.set(incident_key, incident_data, 86400)  # Keep for 24 hours
    
    def generate_honeypot_fields_html(self) -> str:
        """
        Generate HTML for honeypot fields (to be included in forms)
        """
        html_parts = []
        
        for field in self.honeypot_fields:
            html_parts.append(
                f'<input type="text" name="{field}" value="" '
                f'style="position: absolute; left: -9999px; top: -9999px; '
                f'width: 1px; height: 1px; opacity: 0;" '
                f'tabindex="-1" autocomplete="off" />'
            )
        
        return '\n'.join(html_parts)
    
    def generate_javascript_validation(self) -> str:
        """
        Generate JavaScript code for client-side validation
        """
        return """
        <script>
        (function() {
            var formLoadTime = Date.now();
            var mouseMovements = [];
            var clicks = [];
            var focusEvents = 0;
            var keyEvents = 0;
            var typingStart = null;
            
            // Track mouse movements
            document.addEventListener('mousemove', function(e) {
                mouseMovements.push([e.clientX, e.clientY, Date.now()]);
                if (mouseMovements.length > 50) mouseMovements.shift(); // Keep last 50
            });
            
            // Track clicks
            document.addEventListener('click', function(e) {
                clicks.push([e.clientX, e.clientY, Date.now()]);
            });
            
            // Track form interactions
            var formElements = document.querySelectorAll('input, textarea, select');
            formElements.forEach(function(element) {
                element.addEventListener('focus', function() {
                    focusEvents++;
                });
                
                element.addEventListener('keydown', function() {
                    if (typingStart === null) typingStart = Date.now();
                    keyEvents++;
                });
            });
            
            // Add hidden fields to form on submit
            var form = document.getElementById('newsletter-form');
            if (form) {
                form.addEventListener('submit', function() {
                    var now = Date.now();
                    var typingDuration = typingStart ? (now - typingStart) / 1000 : 0;
                    
                    // Add timing data
                    addHiddenField('form_load_timestamp', formLoadTime / 1000);
                    addHiddenField('submission_timestamp', now / 1000);
                    
                    // Add interaction data
                    addHiddenField('form_interactions', JSON.stringify({
                        focus_events: focusEvents,
                        key_events: keyEvents,
                        typing_duration: typingDuration
                    }));
                    
                    // Add mouse data
                    addHiddenField('mouse_movements', JSON.stringify({
                        movements: mouseMovements.slice(-10), // Last 10 movements
                        clicks: clicks
                    }));
                    
                    // Add validation token
                    var token = btoa(String.fromCharCode.apply(null, 
                        new Uint8Array(crypto.getRandomValues(new Uint8Array(16)))));
                    addHiddenField('js_validation_token', token);
                });
            }
            
            function addHiddenField(name, value) {
                var input = document.createElement('input');
                input.type = 'hidden';
                input.name = name;
                input.value = value;
                form.appendChild(input);
            }
        })();
        </script>
        """


# Global instance
honeypot_protection = HoneypotProtection()
